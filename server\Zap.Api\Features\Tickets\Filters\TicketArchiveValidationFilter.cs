using Microsoft.EntityFrameworkCore;
using Zap.Api.Data;

namespace Zap.Api.Features.Tickets.Filters;

internal static class TicketArchiveFiltersExtensions
{
    /// <summary>
    /// Validates that the ticket is not archived before allowing certain operations.
    /// Only allows unarchive and edit name/description operations on archived tickets.
    /// </summary>
    /// <returns>
    /// Returns <see cref="TypedResults.NotFound"/> if the resource is not found,
    /// <see cref="TypedResults.BadRequest"/> if the ticket is archived and operation is not allowed,
    /// or the result of <paramref name="next"/> if the operation is allowed.
    /// </returns>
    internal static RouteHandlerBuilder WithTicketArchiveValidation(this RouteHandlerBuilder builder) =>
        builder.AddEndpointFilter<TicketArchiveValidationFilter>();

    private class TicketArchiveValidationFilter(AppDbContext db) : IEndpointFilter
    {
        public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
        {
            var ticketId = context.GetArgument<string>(0);

            var ticket = await db.Tickets
                .Where(t => t.Id == ticketId)
                .Select(t => new { t.IsArchived })
                .FirstOrDefaultAsync();

            if (ticket == null) return TypedResults.NotFound();

            // If ticket is archived, only allow specific operations
            if (ticket.IsArchived)
            {
                var httpContext = context.HttpContext;
                var endpoint = httpContext.GetEndpoint();
                var endpointName = endpoint?.DisplayName ?? "";

                // Allow archive/unarchive operations
                if (endpointName.Contains("ArchiveTicket") || 
                    httpContext.Request.Path.Value?.Contains("/archive") == true)
                {
                    return await next(context);
                }

                // Allow update operations (for name and description editing)
                if (endpointName.Contains("UpdateTicket") || 
                    httpContext.Request.Method == "PUT" && !httpContext.Request.Path.Value?.Contains("/archive") == true)
                {
                    return await next(context);
                }

                // Block all other operations on archived tickets
                return TypedResults.BadRequest("Cannot perform this operation on an archived ticket. Only unarchive and edit name/description are allowed.");
            }

            return await next(context);
        }
    }
}
