using Microsoft.EntityFrameworkCore;
using Zap.Api.Data.Models;
using Zap.Api.Features.Projects.Services;
using static Zap.Tests.IntegrationTests.CompaniesTests;

namespace Zap.Tests.IntegrationTests;

public class ProjectArchiveTests : IAsyncDisposable
{
    private readonly ZapApplication _app;
    private readonly AppDbContext _db;

    public ProjectArchiveTests()
    {
        _app = new ZapApplication();
        _db = _app.CreateAppDbContext();
    }

    [Fact]
    public async Task ToggleArchiveProject_ShouldArchiveAllTickets_WhenProjectIsArchived()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var companyId = Guid.NewGuid().ToString();
        var projectId = Guid.NewGuid().ToString();
        var ticket1Id = Guid.NewGuid().ToString();
        var ticket2Id = Guid.NewGuid().ToString();

        var user = new User
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Test",
            LastName = "User"
        };

        var project = new Project
        {
            Id = projectId,
            Name = "Test Project",
            Description = "Test Project Description",
            Priority = "High",
            CompanyId = companyId,
            DueDate = DateTime.UtcNow.AddDays(30),
            IsArchived = false
        };

        // Get existing entities from database
        var priority = await _db.TicketPriorities.FirstAsync();
        var status = await _db.TicketStatuses.FirstAsync();
        var type = await _db.TicketTypes.FirstAsync();

        await CreateTestCompany(_db, userId, user, [project], companyId);

        // Get the created company member to use as submitter
        var submitter = await _db.CompanyMembers.FirstAsync(cm => cm.UserId == userId);

        var ticket1 = new Ticket
        {
            Id = ticket1Id,
            Name = "Test Ticket 1",
            Description = "Test Description 1",
            ProjectId = projectId,
            PriorityId = priority.Id,
            StatusId = status.Id,
            TypeId = type.Id,
            SubmitterId = submitter.Id,
            IsArchived = false
        };

        var ticket2 = new Ticket
        {
            Id = ticket2Id,
            Name = "Test Ticket 2",
            Description = "Test Description 2",
            ProjectId = projectId,
            PriorityId = priority.Id,
            StatusId = status.Id,
            TypeId = type.Id,
            SubmitterId = submitter.Id,
            IsArchived = false
        };

        // Add tickets to the database
        _db.Tickets.AddRange(ticket1, ticket2);
        await _db.SaveChangesAsync();

        var projectService = new ProjectService(_db);

        // Act
        var result = await projectService.ToggleArchiveProjectAsync(projectId);

        // Assert
        Assert.True(result);

        // Verify project is archived
        var updatedProject = await _db.Projects.FindAsync(projectId);
        Assert.NotNull(updatedProject);
        Assert.True(updatedProject.IsArchived);

        // Verify all tickets are archived
        var tickets = await _db.Tickets.Where(t => t.ProjectId == projectId).ToListAsync();
        Assert.All(tickets, ticket => Assert.True(ticket.IsArchived));
    }

    [Fact]
    public async Task ToggleArchiveProject_ShouldNotArchiveTickets_WhenProjectIsUnarchived()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var companyId = Guid.NewGuid().ToString();
        var projectId = Guid.NewGuid().ToString();
        var ticketId = Guid.NewGuid().ToString();

        var user = new User
        {
            Id = userId,
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Test",
            LastName = "User"
        };

        var project = new Project
        {
            Id = projectId,
            Name = "Test Project",
            Description = "Test Project Description",
            Priority = "High",
            CompanyId = companyId,
            DueDate = DateTime.UtcNow.AddDays(30),
            IsArchived = true // Start archived
        };

        // Get existing entities from database
        var priority = await _db.TicketPriorities.FirstAsync();
        var status = await _db.TicketStatuses.FirstAsync();
        var type = await _db.TicketTypes.FirstAsync();

        await CreateTestCompany(_db, userId, user, [project], companyId);

        // Get the created company member to use as submitter
        var submitter = await _db.CompanyMembers.FirstAsync(cm => cm.UserId == userId);

        var ticket = new Ticket
        {
            Id = ticketId,
            Name = "Test Ticket",
            Description = "Test Description",
            ProjectId = projectId,
            PriorityId = priority.Id,
            StatusId = status.Id,
            TypeId = type.Id,
            SubmitterId = submitter.Id,
            IsArchived = true // Start archived
        };

        _db.Tickets.Add(ticket);
        await _db.SaveChangesAsync();

        var projectService = new ProjectService(_db);

        // Act - Unarchive the project
        var result = await projectService.ToggleArchiveProjectAsync(projectId);

        // Assert
        Assert.True(result);

        // Verify project is unarchived
        var updatedProject = await _db.Projects.FindAsync(projectId);
        Assert.NotNull(updatedProject);
        Assert.False(updatedProject.IsArchived);

        // Verify ticket remains archived (unarchiving project doesn't unarchive tickets)
        var updatedTicket = await _db.Tickets.FindAsync(ticketId);
        Assert.NotNull(updatedTicket);
        Assert.True(updatedTicket.IsArchived);
    }

    public async ValueTask DisposeAsync()
    {
        await _app.DisposeAsync();
        await _db.DisposeAsync();
    }
}
